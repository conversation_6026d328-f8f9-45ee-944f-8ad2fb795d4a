16:07:28.923 › { module: 'main' } 📁 主进程接收到拖拽文件: 1 个
16:07:28.930 › { module: 'main' } 📁 处理完成，共 16 个文件
16:07:31.134 › { module: 'tus' } 📊 开始智能打包分析: 16 个文件
16:07:31.150 › { module: 'tus' } 📦 开始智能打包上传: 16 个文件
16:07:31.151 › { module: 'tus' } 📦 文件路径示例: /Users/<USER>/Downloads/测试/test/123/123/123/M800003rkqjL0euqRB.mp3, /Users/<USER>/Downloads/测试/test/123/123/123/M800003rkqjL0euqRB_副本.mp3, /Users/<USER>/Downloads/测试/test/123/123/123/M800003rkqjL0euqRB_副本10.mp3...
16:07:31.151 › { module: 'tus' } 📊 开始智能打包分析: 16 个文件
16:07:31.153 › { module: 'tus' } 📊 智能打包分析结果: {"shouldPack":true,"reason":"文件数量 16 超过阈值 10，建议打包上传","fileCount":16,"totalSize":51679920,"estimatedPackTime":5,"estimatedSavings":{"uploadTime":30,"bandwidth":5167992}}
16:07:31.153 › { module: 'tus' } 📦 压缩包名称: test
16:07:31.153 › { module: 'tus' } 📦 7zip-bin检查通过，由ArchiveManager管理
16:07:31.153 › { module: 'tus' } 📦 创建压缩任务: test, 文件数: 16
16:07:31.154 › { module: 'tus' } 📦 压缩任务已创建: archive_1753258051153_ffsih7jkb
16:07:31.154 › { module: 'tus' } 📦 设置事件监听器
16:07:31.154 › { module: 'tus' } 📦 开始压缩任务: archive_1753258051153_ffsih7jkb
16:07:31.154 › { module: 'archive' } 开始压缩: test (16 个文件)
16:07:31.267 › { module: 'archive' } 压缩完成: test
16:07:31.267 › { module: 'tus' } 📦 压缩任务完成: archive_1753258051153_ffsih7jkb {"success":true,"archivePath":"/var/folders/74/ypz2pg996kdb4q105z7g821c0000gn/T/clouddrive-archives/test.7z","stats":{"originalSize":51679920,"compressedSize":51680156,"compressionRatio":-0.0004566570536379544,"fileCount":16,"duration":112}}
16:07:31.267 › { module: 'tus' } 📦 压缩成功，开始上传: /var/folders/74/ypz2pg996kdb4q105z7g821c0000gn/T/clouddrive-archives/test.7z
16:07:31.267 › { module: 'tus' } 📦 压缩任务已启动: archive_1753258051153_ffsih7jkb
16:07:31.290 › { module: 'tus' } 📦 上传任务已创建: upload_1753258051268_yimgsyh68
16:07:31.311 › { module: 'tus' } 📦 智能打包上传已开始: 16 个文件打包为 test.7z
16:07:31.451 › { module: 'tus' } 上传开始后保存URL: test.7z, URL: http://172.20.22.137:8080/files/20250723/laikeke/160731_6880984362eaa