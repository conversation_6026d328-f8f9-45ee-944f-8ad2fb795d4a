# 智能打包路径处理修复

## 问题描述

在智能打包功能中，当上传包含深层嵌套路径的文件夹时，7z打包过程会丢失中间的路径层级。

### 具体现象

- **原始文件路径**：`/Users/<USER>/Downloads/测试/test/123/123/123/M800003rkqjL0euqRB.mp3`
- **期望的7z包内路径**：`test/123/123/123/M800003rkqjL0euqRB.mp3`（保持从选中根目录开始的完整相对路径结构）
- **实际的7z包内路径**：`M800003rkqjL0euqRB.mp3`（只保留了文件名，丢失了`test/123/123/123`等中间层级）

## 问题根因

原有的智能打包逻辑使用 `findTrueCommonParent` 方法来计算所有文件的公共父目录，然后基于这个公共父目录计算相对路径。

当所有文件都在同一个深层目录下时（如 `/Users/<USER>/Downloads/测试/test/123/123/123/`），公共父目录就会被设置为这个深层目录，导致相对路径只是文件名，丢失了中间的路径层级。

**问题代码示例**：
```javascript
// 所有文件都在同一个深层目录下
const filePaths = [
  '/Users/<USER>/Downloads/测试/test/123/123/123/file1.mp3',
  '/Users/<USER>/Downloads/测试/test/123/123/123/file2.mp3'
];

// findTrueCommonParent 会返回 '/Users/<USER>/Downloads/测试/test/123/123/123'
// 导致相对路径只是 ['file1.mp3', 'file2.mp3']
```

## 修复方案

### 核心思路

在智能打包接口中添加可选的 `rootPath` 参数，当用户通过文件夹选择进行上传时，传递用户选择的文件夹路径作为根目录，确保相对路径基于用户选择的根目录计算。

### 修改内容

#### 1. 类型定义修改

**文件**: `electron/archive/types.ts`
- 在 `ArchiveOptions` 接口中添加 `rootPath?: string` 字段
- 在 `ArchiveTask` 接口中添加 `rootPath?: string` 字段

#### 2. 核心逻辑修改

**文件**: `electron/archive/archiveManager.ts`
- 修改 `calculateRelativePaths` 方法，添加 `rootPath` 参数支持
- 当提供 `rootPath` 时，使用它作为工作目录而不是计算的公共父目录
- 在 `createArchiveTask` 中保存 `rootPath` 到任务对象
- 在 `performCompression` 中传递任务的 `rootPath` 给路径计算方法

#### 3. API 接口修改

**文件**: `electron/tus/uploadManager.ts`
- 在 `smartPackUpload` 方法中添加 `rootPath` 参数
- 将 `rootPath` 传递给 `archiveManager.createArchiveTask`

#### 4. IPC 处理器修改

**文件**: `electron/tus/ipcHandlers.ts` 和 `electron/tus/preloadApi.ts`
- 更新 `tus-smart-pack-upload` 处理器的参数类型，添加 `rootPath` 支持

#### 5. 前端调用修改

**文件**: `src/components/Upload/composables/useTusUpload.ts`
- 在 `handleFolderSelectionSmartPacking` 中传递 `folderPath` 作为 `rootPath`

### 修复后的逻辑

```javascript
// 修复后的路径计算逻辑
function calculateRelativePaths(sourcePaths, rootPath) {
  // 如果提供了根目录，使用它作为工作目录
  if (rootPath) {
    const normalizedRootPath = path.resolve(rootPath);
    const normalizedPaths = sourcePaths.map((p) => path.resolve(p));
    
    // 验证所有文件都在根目录下
    const invalidPaths = normalizedPaths.filter(p => !p.startsWith(normalizedRootPath));
    if (invalidPaths.length === 0) {
      // 基于根目录计算相对路径
      const relativePaths = normalizedPaths.map((p) => path.relative(normalizedRootPath, p));
      return {
        workingDir: normalizedRootPath,
        relativePaths,
      };
    }
  }
  
  // 回退到原有逻辑（用于其他场景）
  return calculateRelativePathsOriginal(sourcePaths);
}
```

## 修复效果

### 修复前
- 工作目录：`/Users/<USER>/Downloads/测试/test/123/123/123`
- 相对路径：`['M800003rkqjL0euqRB.mp3', 'M800003rkqjL0euqRB_副本.mp3', ...]`
- 7z包内结构：只有文件名，丢失目录结构

### 修复后
- 工作目录：`/Users/<USER>/Downloads/测试`（用户选择的根目录）
- 相对路径：`['test/123/123/123/M800003rkqjL0euqRB.mp3', 'test/123/123/123/M800003rkqjL0euqRB_副本.mp3', ...]`
- 7z包内结构：保持完整的目录层级关系

## 兼容性

- 修复是向后兼容的，`rootPath` 是可选参数
- 当不提供 `rootPath` 时，使用原有的逻辑（适用于拖拽上传等场景）
- 只有文件夹选择上传会传递 `rootPath`，确保路径结构正确

## 代码清理

修复完成后，已清理所有临时添加的调试代码：

### 已清理的调试内容
- 移除所有包含 `🔧 [路径修复]` 标识的调试日志
- 移除临时添加的 `tusLogger.analysis`、`archiveLogger.info` 等调试输出
- 保留核心功能代码和必要的错误处理日志

### 保留的内容
- 所有核心功能逻辑（rootPath 参数支持、路径计算修复等）
- 原有的正常业务日志
- 错误处理和警告日志
- 接口类型定义的修改

## 测试验证

修复已通过测试验证，能够正确处理深层嵌套文件夹的路径结构，确保7z包内的文件保持从用户选择的根目录开始的完整相对路径。

**注意**：代码已清理完毕，可以正常使用，不会产生额外的调试日志输出。
