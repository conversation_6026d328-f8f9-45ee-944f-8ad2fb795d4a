import { ref, computed, onMounted, onUnmounted } from "vue";
import type { UploadTask, TusUploadConfig, ApiResponse, BatchUploadTask } from "@/types/electron";
import { debug } from "@/lib/debug";
import { generateUniqueId } from "@/lib/utils";
import { formatFileSize } from "@/lib/upload-utils";
import { toast } from "vue-sonner";
import { config } from "@/config";

// 上传任务状态类型
export type UploadStatus = "pending" | "uploading" | "paused" | "completed" | "error" | "cancelled";

// 重新导出类型以便其他组件使用
export type { UploadTask, TusUploadConfig };

// 上传回调接口
export interface UploadCallbacks {
  onFileUploaded?: (file: File, task: UploadTask) => void;
  onAllFilesUploaded?: (files: File[], completedTasks: UploadTask[]) => void;
  onUploadError?: (error: string, failedTasks: UploadTask[]) => void;
}

// 上传组跟踪接口
interface UploadGroup {
  id: string;
  files: File[];
  taskIds: string[];
  taskFileMap: Map<string, File>;
  callbacks?: UploadCallbacks;
  completedCount: number;
  errorCount: number;
  startTime: Date;
}

// ========== 统一日志管理器 ==========
interface TusLogConfig {
  enabled: boolean;
  showAnalysis: boolean;
  showEvents: boolean;
  showBatch: boolean;
  showTasks: boolean;
  showErrors: boolean;
}

const TUS_LOG_CONFIG: TusLogConfig = {
  enabled: import.meta.env.DEV, // 开发环境默认开启
  showAnalysis: true, // 显示上传分析日志
  showEvents: true, // 显示事件日志
  showBatch: true, // 显示批量操作日志
  showTasks: true, // 显示任务状态日志
  showErrors: true, // 显示错误日志（总是建议开启）
};

class TusLogger {
  private static instance: TusLogger;
  private config: TusLogConfig;

  private constructor() {
    this.config = { ...TUS_LOG_CONFIG };
  }

  static getInstance(): TusLogger {
    if (!TusLogger.instance) {
      TusLogger.instance = new TusLogger();
    }
    return TusLogger.instance;
  }

  // 更新配置
  updateConfig(config: Partial<TusLogConfig>) {
    this.config = { ...this.config, ...config };
  }

  // 获取当前配置
  getConfig(): TusLogConfig {
    return { ...this.config };
  }

  // 私有日志方法
  private log(category: keyof TusLogConfig, level: "log" | "warn" | "error", message: string, ...args: any[]) {
    if (!this.config.enabled) return;
    if (category !== "enabled" && !this.config[category]) return;

    const prefix = `[TUS-${category.toUpperCase()}]`;
    console[level](prefix, message, ...args);
  }

  // 公共日志方法
  analysis(message: string, ...args: any[]) {
    this.log("showAnalysis", "log", message, ...args);
  }

  event(message: string, ...args: any[]) {
    this.log("showEvents", "log", message, ...args);
  }

  batch(message: string, ...args: any[]) {
    this.log("showBatch", "log", message, ...args);
  }

  task(message: string, ...args: any[]) {
    this.log("showTasks", "log", message, ...args);
  }

  error(message: string, ...args: any[]) {
    this.log("showErrors", "error", message, ...args);
  }

  warn(message: string, ...args: any[]) {
    this.log("showErrors", "warn", message, ...args);
  }

  // 特殊方法：总是输出（忽略配置）
  always(message: string, ...args: any[]) {
    if (this.config.enabled) {
      console.log("[TUS]", message, ...args);
    }
  }
}

// 创建全局日志实例
const tusLogger = TusLogger.getInstance();

// 导出日志配置管理函数
export const useTusLogger = () => {
  const updateLogConfig = (config: Partial<TusLogConfig>) => {
    tusLogger.updateConfig(config);
  };

  const getLogConfig = () => {
    return tusLogger.getConfig();
  };

  const enableAllLogs = () => {
    tusLogger.updateConfig({
      enabled: true,
      showAnalysis: true,
      showEvents: true,
      showBatch: true,
      showTasks: true,
      showErrors: true,
    });
  };

  const disableAllLogs = () => {
    tusLogger.updateConfig({
      enabled: false,
      showAnalysis: false,
      showEvents: false,
      showBatch: false,
      showTasks: false,
      showErrors: false,
    });
  };

  const enableOnlyErrors = () => {
    tusLogger.updateConfig({
      enabled: true,
      showAnalysis: false,
      showEvents: false,
      showBatch: false,
      showTasks: false,
      showErrors: true,
    });
  };

  return {
    updateLogConfig,
    getLogConfig,
    enableAllLogs,
    disableAllLogs,
    enableOnlyErrors,
  };
};

// ========== 核心上传逻辑 ==========

// 全局单例状态
let tusUploadInstance: ReturnType<typeof createTusUploadInstance> | null = null;

function createTusUploadInstance() {
  const isElectron = ref(typeof window !== "undefined" && !!(window as any).electronAPI);
  const tasks = ref<Map<string, UploadTask>>(new Map());
  const batchTasks = ref<Map<string, BatchUploadTask>>(new Map());
  const uploadGroups = ref<Map<string, UploadGroup>>(new Map());

  // 获取 electron API
  const getElectronAPI = () => (window as any).electronAPI;

  // 计算属性：各状态的任务
  const allTasks = computed(() => Array.from(tasks.value.values()));
  const allBatchTasks = computed(() => Array.from(batchTasks.value.values()));

  // 只返回非子任务的单独任务
  const standaloneTasks = computed(() => allTasks.value.filter((task) => !task.isSubTask));

  // 显示的任务列表（批量任务 + 独立任务）
  const displayTasks = computed(() => {
    const result: (UploadTask | BatchUploadTask)[] = [];
    result.push(...allBatchTasks.value);
    result.push(...standaloneTasks.value);
    return result.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime());
  });

  const activeTasks = computed(() => allTasks.value.filter((task) => ["pending", "uploading"].includes(task.status)));
  const uploadingTasks = computed(() => allTasks.value.filter((task) => task.status === "uploading"));
  const pausedTasks = computed(() => allTasks.value.filter((task) => task.status === "paused"));
  const completedTasks = computed(() => allTasks.value.filter((task) => task.status === "completed"));
  const errorTasks = computed(() => allTasks.value.filter((task) => task.status === "error"));

  const activeBatchTasks = computed(() => allBatchTasks.value.filter((task) => ["pending", "uploading"].includes(task.status)));
  const uploadingBatchTasks = computed(() => allBatchTasks.value.filter((task) => task.status === "uploading"));

  const totalProgress = computed(() => {
    const activeStandalone = standaloneTasks.value.filter((task) => ["pending", "uploading"].includes(task.status));
    const activeBatch = activeBatchTasks.value;

    const allActiveItems = [...activeStandalone, ...activeBatch];
    if (allActiveItems.length === 0) return 0;

    return allActiveItems.reduce((sum, task) => sum + task.progress, 0) / allActiveItems.length;
  });

  // 生命周期管理
  const isListenersSetup = ref(false);

  // 事件处理器
  const handleTaskCreated = (taskId: string, task: UploadTask) => {
    debug.task.create(taskId, task.fileName);

    if (tasks.value.has(taskId)) {
      tusLogger.task(`任务已存在，跳过: ${task.fileName}`);
      return;
    }

    tusLogger.event(`任务创建事件 - ${task.fileName} (${taskId})`);

    // 从 metadata 中读取子任务标识并设置到任务对象上
    const enhancedTask = { ...task };
    if (task.metadata?.isSubTask === "true") {
      enhancedTask.isSubTask = true;
      enhancedTask.batchId = task.metadata.batchId;
      tusLogger.batch(`子任务标识已设置: ${task.fileName} -> batchId: ${task.metadata.batchId}`);
    }

    tasks.value.set(taskId, enhancedTask);

    if (enhancedTask.batchId) {
      updateBatchTaskProgress(enhancedTask.batchId);
    }

    // 检查是否为智能打包上传任务，如果是则添加到对应的上传组
    if (task.metadata?.uploadType === "smart-packed-archive" && task.metadata?.uploadGroupId) {
      const uploadGroupId = task.metadata.uploadGroupId;
      const uploadGroup = uploadGroups.value.get(uploadGroupId);
      if (uploadGroup) {
        uploadGroup.taskIds.push(taskId);
        // 为智能打包任务创建一个虚拟文件映射（代表所有原始文件）
        const virtualFile = new File([], `${task.fileName} (${task.metadata.originalFileCount} 个文件)`, {
          type: "application/x-7z-compressed",
        });
        uploadGroup.taskFileMap.set(taskId, virtualFile);
        tusLogger.event(`智能打包任务已添加到上传组: ${uploadGroupId}, 任务ID: ${taskId}`);
      }
    }
  };

  const handleTaskProgress = (taskId: string, progress: number, bytesUploaded: number, _bytesTotal: number) => {
    const task = tasks.value.get(taskId);
    if (task) {
      const updatedTask = {
        ...task,
        progress: Math.round(progress),
        bytesUploaded,
        status: task.status === "pending" ? ("uploading" as const) : task.status,
      };
      tasks.value.set(taskId, updatedTask);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }
    }
  };

  const handleTaskStatusChanged = async (taskId: string, status: string, error?: string) => {
    const task = tasks.value.get(taskId);
    if (task) {
      const updatedTask = {
        ...task,
        status: status as UploadStatus,
        error,
      };

      if (["completed", "error", "cancelled"].includes(status)) {
        updatedTask.progress = status === "completed" ? 100 : task.progress;

        // 如果任务完成且没有 uploadUrl，尝试获取
        if (status === "completed" && !task.uploadUrl) {
          try {
            const uploadUrl = await getTaskUploadUrl(taskId);
            if (uploadUrl) {
              updatedTask.uploadUrl = uploadUrl;
              tusLogger.task(`任务完成时获取上传URL: ${task.fileName} -> ${uploadUrl}`);
            }
          } catch (error) {
            tusLogger.warn(`获取上传URL失败: ${task.fileName}`, error);
          }
        }
      }

      tasks.value.set(taskId, updatedTask);

      tusLogger.task(`任务状态变化: ${task.fileName} -> ${status}${error ? ` (错误: ${error})` : ""}`);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }

      if (status && status !== task.status) {
        debug.task.status(task.fileName, task.status, status);
      }

      if (status === "completed") {
        debug.upload.complete(task.fileName);
      } else if (status === "error" && error) {
        debug.upload.error(task.fileName, error);
      }
    }
  };

  const handleTaskCompleted = async (taskId: string) => {
    // 使用辅助方法确保任务信息完整
    const completeTask = await ensureTaskCompleteness(taskId);
    if (completeTask) {
      const updatedTask = {
        ...completeTask,
        status: "completed" as UploadStatus,
        progress: 100,
      };
      tasks.value.set(taskId, updatedTask);

      if (completeTask.batchId) {
        updateBatchTaskProgress(completeTask.batchId);
      }

      debug.upload.complete(completeTask.fileName);

      // 检查是否属于某个上传组，触发相应回调
      checkUploadGroupProgress(taskId, completeTask);
    }
  };

  const handleTaskError = (taskId: string, error: string) => {
    const task = tasks.value.get(taskId);
    if (task) {
      const updatedTask = { ...task, status: "error" as UploadStatus, error };
      tasks.value.set(taskId, updatedTask);
      tusLogger.error(`任务出错 - ${task.fileName}: ${error}`);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }

      debug.upload.error(task.fileName, error);

      // 更新上传组错误状态
      updateUploadGroupOnError(taskId);
    }
  };

  // ========== 智能打包辅助函数 ==========

  /**
   * 智能打包结果接口
   */
  interface SmartPackingResult {
    handled: boolean;
    error?: string;
  }

  /**
   * 检查是否应该进行智能打包
   */
  const shouldTrySmartPacking = (files: File[]): boolean => {
    const smartPackingThreshold = config.smartPacking.threshold;
    const hasFilePaths = files.some((file) => (file as any).path || (file as any).webkitRelativePath);
    return hasFilePaths && files.length >= smartPackingThreshold;
  };

  /**
   * 提取文件路径（绝对路径或相对路径）
   */
  const extractFilePaths = (files: File[]): string[] => {
    return files
      .map((file) => {
        const absolutePath = (file as any).path;
        const relativePath = (file as any).webkitRelativePath;

        if (absolutePath) {
          return absolutePath;
        } else if (relativePath) {
          tusLogger.warn(`文件 ${file.name} 只有相对路径: ${relativePath}`);
          return relativePath;
        }
        return null;
      })
      .filter(Boolean) as string[];
  };

  /**
   * 检查路径类型
   */
  const analyzePathTypes = (filePaths: string[]) => {
    const hasAbsolutePaths = filePaths.some((path) => path.startsWith("/") || path.match(/^[A-Z]:\\/));
    return { hasAbsolutePaths };
  };

  /**
   * 检查是否为拖拽上传
   */
  const isDragUpload = (files: File[]): boolean => {
    return files.some((file) => (file as any).webkitRelativePath) && !files.some((file) => (file as any).path);
  };

  /**
   * 创建智能打包上传组
   */
  const createSmartPackUploadGroup = (uploadGroupId: string, files: File[], callbacks?: UploadCallbacks): UploadGroup => {
    return {
      id: uploadGroupId,
      files: [...files],
      taskIds: [],
      taskFileMap: new Map(),
      callbacks,
      completedCount: 0,
      errorCount: 0,
      startTime: new Date(),
    };
  };

  /**
   * 清理文件名中的特殊字符，确保符合文件系统命名规范
   */
  const sanitizeFileName = (fileName: string): string => {
    // 移除或替换不安全的字符
    return fileName
      .replace(/[<>:"/\\|?*]/g, "_") // 替换文件系统不允许的字符
      .replace(/\s+/g, "_") // 将空格替换为下划线
      .replace(/_{2,}/g, "_") // 将多个连续下划线替换为单个
      .replace(/^_+|_+$/g, ""); // 移除开头和结尾的下划线
  };

  /**
   * 从文件路径中提取文件/文件夹名称
   */
  const extractFileOrFolderName = (filePath: string): string => {
    // 处理绝对路径
    if (filePath.includes("/") || filePath.includes("\\")) {
      const parts = filePath.replace(/\\/g, "/").split("/");
      return parts[parts.length - 1] || parts[parts.length - 2] || "unknown";
    }
    return filePath;
  };

  /**
   * 生成智能打包压缩文件名
   */
  const generateSmartPackArchiveName = (files: File[], analysis?: ReturnType<typeof analyzeFileGroups>, folderPath?: string): string => {
    const MAX_NAME_LENGTH = 100;
    const SUFFIX = "等文件";
    const MAX_FILES_TO_SHOW = 5;

    // 单文件夹上传场景：使用文件夹名称
    if (analysis?.folderGroups.length === 1 && analysis.singleFiles.length === 0) {
      const folderName = analysis.folderGroups[0].name;
      const sanitizedName = sanitizeFileName(folderName);
      return sanitizedName.length > MAX_NAME_LENGTH ? sanitizedName.substring(0, MAX_NAME_LENGTH - 3) + "..." : sanitizedName;
    }

    // 如果有folderPath参数（来自文件夹选择），优先使用
    if (folderPath) {
      const folderName = extractFileOrFolderName(folderPath);
      const sanitizedName = sanitizeFileName(folderName);
      return sanitizedName.length > MAX_NAME_LENGTH ? sanitizedName.substring(0, MAX_NAME_LENGTH - 3) + "..." : sanitizedName;
    }

    // 多文件/多文件夹场景：生成组合名称
    const fileNames: string[] = [];

    // 收集文件名
    if (analysis) {
      // 添加单独文件名
      analysis.singleFiles.forEach((file) => {
        const fileName = extractFileOrFolderName(file.name);
        fileNames.push(sanitizeFileName(fileName));
      });

      // 添加文件夹名
      analysis.folderGroups.forEach((folder) => {
        fileNames.push(sanitizeFileName(folder.name));
      });
    } else {
      // 如果没有analysis，直接从files中提取
      files.forEach((file) => {
        let fileName: string;
        const relativePath = (file as any).webkitRelativePath;
        const absolutePath = (file as any).path;

        if (relativePath) {
          // 对于有相对路径的文件，提取根文件夹名
          fileName = relativePath.split("/")[0];
        } else if (absolutePath) {
          fileName = extractFileOrFolderName(absolutePath);
        } else {
          fileName = file.name;
        }

        const sanitizedName = sanitizeFileName(fileName);
        if (!fileNames.includes(sanitizedName)) {
          fileNames.push(sanitizedName);
        }
      });
    }

    // 去重并限制数量
    const uniqueNames = [...new Set(fileNames)];
    const namesToShow = uniqueNames.slice(0, MAX_FILES_TO_SHOW);

    // 生成基础名称
    let baseName: string;
    if (namesToShow.length === 0) {
      baseName = `batch_upload_${Date.now()}`;
    } else if (namesToShow.length === 1) {
      baseName = namesToShow[0];
    } else {
      const joinedNames = namesToShow.join(",");
      baseName = uniqueNames.length > MAX_FILES_TO_SHOW ? `${joinedNames}${SUFFIX}` : joinedNames;
    }

    // 处理长度限制
    if (baseName.length > MAX_NAME_LENGTH) {
      if (baseName.endsWith(SUFFIX)) {
        // 如果以"等文件"结尾，确保截断后仍然保持这个后缀
        const maxContentLength = MAX_NAME_LENGTH - SUFFIX.length;
        const truncatedContent = baseName.substring(0, maxContentLength);
        // 找到最后一个逗号，避免在文件名中间截断
        const lastCommaIndex = truncatedContent.lastIndexOf(",");
        if (lastCommaIndex > 0) {
          baseName = truncatedContent.substring(0, lastCommaIndex) + SUFFIX;
        } else {
          baseName = truncatedContent + SUFFIX;
        }
      } else {
        baseName = baseName.substring(0, MAX_NAME_LENGTH - 3) + "...";
      }
    }

    return baseName || `batch_upload_${Date.now()}`;
  };

  /**
   * 处理拖拽上传的智能打包
   */
  const handleDragUploadSmartPacking = async (files: File[], enhancedMetadata: Record<string, string>, uploadGroup: UploadGroup): Promise<SmartPackingResult> => {
    tusLogger.analysis(`📦 检测到拖拽上传 ${files.length} 个文件，尝试智能打包`);

    try {
      const api = getElectronAPI();

      // 从拖拽文件中提取根目录路径
      const firstFileWithPath = files.find((file) => (file as any).webkitRelativePath);
      if (!firstFileWithPath) {
        tusLogger.analysis(`📦 拖拽文件没有相对路径信息，回退到批量上传`);
        await uploadAsBatch(files, enhancedMetadata, uploadGroup);
        toast.success(`拖拽批量上传已开始: ${files.length} 个文件`);
        return { handled: true };
      }

      const relativePath = (firstFileWithPath as any).webkitRelativePath as string;
      const rootFolderName = relativePath.split("/")[0];

      tusLogger.analysis(`🔧 [路径修复] 拖拽上传 - 根文件夹名: ${rootFolderName}`);

      // 构造文件的绝对路径（基于临时目录）
      const filePaths = files.map((file) => {
        const webkitPath = (file as any).webkitRelativePath;
        if (webkitPath) {
          // 使用一个虚拟的根路径来模拟绝对路径
          return `/tmp/drag_upload/${webkitPath}`;
        }
        return `/tmp/drag_upload/${file.name}`;
      });

      tusLogger.analysis(`🔧 [路径修复] 拖拽上传 - 构造的文件路径示例: ${filePaths.slice(0, 3).join(", ")}`);

      const smartPackingThreshold = config.smartPacking.threshold;
      if (files.length < smartPackingThreshold) {
        tusLogger.analysis(`📦 文件数量 ${files.length} 未达到智能打包阈值 ${smartPackingThreshold}，使用批量上传`);
        await uploadAsBatch(files, enhancedMetadata, uploadGroup);
        toast.success(`拖拽批量上传已开始: ${files.length} 个文件`);
        return { handled: true };
      }

      const archiveName = generateSmartPackArchiveName(files, undefined, rootFolderName);
      tusLogger.analysis(`🔧 [路径修复] 拖拽上传 - 生成压缩包名称: ${archiveName}`);

      // 对于拖拽上传，我们使用虚拟根路径
      const virtualRootPath = `/tmp/drag_upload`;

      const smartPackResponse = await api.tus.smartPackUpload(filePaths, {
        threshold: smartPackingThreshold,
        archiveName,
        metadata: enhancedMetadata,
        rootPath: virtualRootPath, // 使用虚拟根路径
      });

      if (!smartPackResponse.success) {
        throw new Error(smartPackResponse.error || "拖拽智能打包上传失败");
      }

      toast.success(`拖拽智能打包上传已开始: ${files.length} 个文件`);
      tusLogger.analysis(`🔧 [路径修复] 拖拽智能打包上传成功，rootPath: ${virtualRootPath}`);

      // 添加任务到上传组
      if (smartPackResponse.taskId) {
        uploadGroup.taskIds.push(smartPackResponse.taskId);
      }

      return { handled: true };
    } catch (error) {
      tusLogger.error(`📦 拖拽智能打包失败，回退到批量上传:`, error);
      await uploadAsBatch(files, enhancedMetadata, uploadGroup);
      toast.success(`拖拽批量上传已开始: ${files.length} 个文件`);
      return { handled: true };
    }
  };

  /**
   * 处理文件夹选择的智能打包
   */
  const handleFolderSelectionSmartPacking = async (
    files: File[],
    enhancedMetadata: Record<string, string>,
    uploadGroupId: string,
    callbacks?: UploadCallbacks,
    analysis?: ReturnType<typeof analyzeFileGroups>
  ): Promise<SmartPackingResult> => {
    try {
      tusLogger.analysis(`📦 非拖拽上传，调用文件夹选择API`);

      const api = getElectronAPI();
      const folderResult = await api.tus.selectFolderForPacking();

      if (!folderResult.success) {
        if (folderResult.error && !folderResult.error.includes("用户取消")) {
          throw new Error(folderResult.error);
        }
        tusLogger.analysis(`📦 用户取消文件夹选择，继续常规上传`);
        return { handled: false };
      }

      if (!folderResult.data) {
        return { handled: false };
      }

      const { folderPath, filePaths: absoluteFilePaths, fileCount } = folderResult.data;
      tusLogger.analysis(`📦 获取到绝对路径: ${absoluteFilePaths.length} 个文件`);

      const smartPackingThreshold = config.smartPacking.threshold;
      if (fileCount < smartPackingThreshold) {
        return { handled: false };
      }

      const archiveName = generateSmartPackArchiveName(files, analysis, folderPath);
      tusLogger.analysis(`📦 生成压缩包名称: ${archiveName}`);

      tusLogger.analysis(`🔧 [路径修复] 文件夹选择上传 - folderPath: ${folderPath}`);
      tusLogger.analysis(`🔧 [路径修复] 文件夹选择上传 - 文件路径示例: ${absoluteFilePaths.slice(0, 3).join(", ")}`);

      const smartPackResponse = await api.tus.smartPackUpload(absoluteFilePaths, {
        threshold: smartPackingThreshold,
        archiveName,
        metadata: enhancedMetadata,
        rootPath: folderPath, // 传递用户选择的文件夹路径作为根目录
      });

      tusLogger.analysis(`🔧 [路径修复] 智能打包调用完成，rootPath已传递: ${folderPath}`);

      if (!smartPackResponse.success) {
        throw new Error(smartPackResponse.error || "智能打包上传失败");
      }

      toast.success(`智能打包上传已开始: ${fileCount} 个文件`);
      tusLogger.analysis(`📦 智能打包上传成功启动，任务ID: ${smartPackResponse.data?.archiveTaskId}`);

      if (smartPackResponse.data?.archiveTaskId) {
        tusLogger.analysis(`📦 智能打包任务已创建，压缩进度将通过事件监听器自动显示: ${archiveName}.7z (${fileCount} 个文件)`);
      }

      if (callbacks) {
        const smartPackUploadGroup = createSmartPackUploadGroup(uploadGroupId, files, callbacks);
        uploadGroups.value.set(uploadGroupId, smartPackUploadGroup);
        tusLogger.event(`创建智能打包上传组: ${uploadGroupId}, 包含 ${files.length} 个文件`);
      }

      return { handled: true };
    } catch (error) {
      tusLogger.error("智能打包上传失败:", error);
      toast.error(`智能打包失败: ${error}，将回退到常规上传`);
      return { handled: false, error: String(error) };
    }
  };

  /**
   * 处理绝对路径的智能打包
   */
  const handleAbsolutePathSmartPacking = async (
    files: File[],
    filePaths: string[],
    enhancedMetadata: Record<string, string>,
    uploadGroupId: string,
    callbacks?: UploadCallbacks,
    analysis?: ReturnType<typeof analyzeFileGroups>
  ): Promise<SmartPackingResult> => {
    try {
      const api = getElectronAPI();
      const smartPackingThreshold = config.smartPacking.threshold;

      const analysisResponse = await api.tus.analyzeSmartPacking(filePaths, {
        threshold: smartPackingThreshold,
      });

      if (!analysisResponse.success || !analysisResponse.data?.shouldPack) {
        return { handled: false };
      }

      tusLogger.analysis(`📦 智能打包建议: ${analysisResponse.data.reason}`);
      tusLogger.analysis(`📦 智能打包默认启用，开始打包上传`);
      tusLogger.analysis(`📦 调用智能打包上传API，文件数: ${filePaths.length}`);

      const archiveName = generateSmartPackArchiveName(files, analysis);
      tusLogger.analysis(`📦 生成压缩包名称: ${archiveName}`);

      // 对于拖拽上传，尝试从文件路径中推断根目录
      let rootPath: string | undefined;
      if (files.some((file) => (file as any).webkitRelativePath)) {
        // 拖拽上传：从相对路径推断根目录
        const firstFileWithPath = files.find((file) => (file as any).webkitRelativePath);
        if (firstFileWithPath) {
          const relativePath = (firstFileWithPath as any).webkitRelativePath as string;
          const rootFolderName = relativePath.split("/")[0];
          // 从绝对路径中推断根目录
          const firstAbsolutePath = filePaths[0];
          const pathParts = firstAbsolutePath.split("/");
          const rootIndex = pathParts.findIndex((part) => part === rootFolderName);
          if (rootIndex > 0) {
            rootPath = pathParts.slice(0, rootIndex).join("/");
            tusLogger.analysis(`🔧 [路径修复] 拖拽上传推断根目录: ${rootPath}`);
          }
        }
      }

      tusLogger.analysis(`🔧 [路径修复] 绝对路径智能打包 - rootPath: ${rootPath || "未推断出"}`);
      tusLogger.analysis(`🔧 [路径修复] 绝对路径智能打包 - 文件路径示例: ${filePaths.slice(0, 3).join(", ")}`);

      const smartPackResponse = await api.tus.smartPackUpload(filePaths, {
        threshold: smartPackingThreshold,
        archiveName,
        metadata: enhancedMetadata,
        rootPath, // 传递推断出的根目录
      });

      tusLogger.analysis(`📦 智能打包上传API响应:`, smartPackResponse);

      if (!smartPackResponse.success) {
        throw new Error(smartPackResponse.error || "智能打包上传失败");
      }

      toast.success(`智能打包上传已开始: ${files.length} 个文件`);
      tusLogger.analysis(`📦 智能打包上传成功启动，任务ID: ${smartPackResponse.data?.archiveTaskId}`);

      if (smartPackResponse.data?.archiveTaskId) {
        tusLogger.analysis(`📦 智能打包任务已创建，压缩进度将通过事件监听器自动显示: ${archiveName}.7z (${files.length} 个文件)`);
      }

      if (callbacks) {
        const smartPackUploadGroup = createSmartPackUploadGroup(uploadGroupId, files, callbacks);
        uploadGroups.value.set(uploadGroupId, smartPackUploadGroup);
        tusLogger.event(`创建智能打包上传组: ${uploadGroupId}, 包含 ${files.length} 个文件`);
      }

      return { handled: true };
    } catch (error) {
      tusLogger.error("智能打包上传失败:", error);
      toast.error(`智能打包失败: ${error}，将回退到常规上传`);
      return { handled: false, error: String(error) };
    }
  };

  /**
   * 主要的智能打包处理函数
   */
  const trySmartPacking = async (
    files: File[],
    enhancedMetadata: Record<string, string>,
    uploadGroupId: string,
    callbacks?: UploadCallbacks,
    analysis?: ReturnType<typeof analyzeFileGroups>
  ): Promise<SmartPackingResult> => {
    // 早期返回：检查是否应该尝试智能打包
    if (!shouldTrySmartPacking(files)) {
      return { handled: false };
    }

    tusLogger.analysis(`📦 检测到大量文件 (${files.length} >= ${config.smartPacking.threshold})，进行智能打包分析`);

    try {
      // 提取文件路径
      const filePaths = extractFilePaths(files);

      // 早期返回：无法获取文件路径
      if (filePaths.length === 0) {
        tusLogger.warn(`📦 无法获取文件路径，跳过智能打包`);
        return { handled: false };
      }

      tusLogger.analysis(`📦 获取到文件路径: ${filePaths.length} 个，示例: ${filePaths.slice(0, 3).join(", ")}`);

      // 分析路径类型
      const { hasAbsolutePaths } = analyzePathTypes(filePaths);

      // 处理相对路径情况
      if (!hasAbsolutePaths && files.some((file) => (file as any).webkitRelativePath)) {
        tusLogger.analysis(`📦 检测到拖拽上传的文件夹结构，尝试智能打包`);

        if (isDragUpload(files)) {
          // 创建上传组用于拖拽上传
          const uploadGroup: UploadGroup = {
            id: uploadGroupId,
            files: [...files],
            taskIds: [],
            taskFileMap: new Map(),
            callbacks,
            completedCount: 0,
            errorCount: 0,
            startTime: new Date(),
          };
          return await handleDragUploadSmartPacking(files, enhancedMetadata, uploadGroup);
        } else {
          return await handleFolderSelectionSmartPacking(files, enhancedMetadata, uploadGroupId, callbacks, analysis);
        }
      }

      // 处理绝对路径情况
      return await handleAbsolutePathSmartPacking(files, filePaths, enhancedMetadata, uploadGroupId, callbacks, analysis);
    } catch (error) {
      tusLogger.warn("智能打包分析失败，回退到常规上传:", error);
      return { handled: false, error: String(error) };
    }
  };

  /**
   * 分析文件组成，区分单独文件和文件夹
   */
  const analyzeFileGroups = (files: File[]) => {
    const singleFiles: File[] = [];
    const folderGroups: { [folderName: string]: File[] } = {};

    files.forEach((file) => {
      const relativePath = (file as any).webkitRelativePath as string;

      if (relativePath) {
        // 有相对路径，说明来自文件夹
        const pathParts = relativePath.split("/");
        const rootFolderName = pathParts[0];

        if (!folderGroups[rootFolderName]) {
          folderGroups[rootFolderName] = [];
        }
        folderGroups[rootFolderName].push(file);
      } else {
        // 没有相对路径，说明是单独选择的文件
        singleFiles.push(file);
      }
    });

    return {
      singleFiles,
      folderGroups: Object.entries(folderGroups).map(([name, files]) => ({
        name,
        files,
      })),
      hasMixedUpload: singleFiles.length > 0 && Object.keys(folderGroups).length > 0,
      totalGroups: singleFiles.length + Object.keys(folderGroups).length,
    };
  };

  /**
   * 检查上传组进度并触发回调
   */
  const checkUploadGroupProgress = (taskId: string, completedTask: UploadTask) => {
    // 查找包含此任务的上传组
    for (const [groupId, group] of uploadGroups.value.entries()) {
      if (group.taskIds.includes(taskId)) {
        group.completedCount++;

        // 使用映射查找对应的文件
        const correspondingFile = group.taskFileMap.get(taskId);

        tusLogger.event(`上传组 ${groupId} 进度: ${group.completedCount}/${group.taskIds.length}`);

        if (correspondingFile) {
          tusLogger.event(`找到对应文件: ${correspondingFile.name} (taskId: ${taskId})`);
        } else {
          tusLogger.warn(`未找到对应文件: taskId=${taskId}`);
        }

        // 触发单个文件上传成功回调
        if (group.callbacks?.onFileUploaded && correspondingFile) {
          try {
            // 检查是否为智能打包上传
            if (completedTask.metadata?.uploadType === "smart-packed-archive") {
              // 智能打包上传：只触发一次回调，传递压缩文件信息
              // 创建一个代表压缩文件的File对象
              const archiveFile = new File([], completedTask.fileName, {
                type: "application/x-7z-compressed",
              });
              group.callbacks.onFileUploaded(archiveFile, completedTask);
              tusLogger.event(`智能打包回调已触发: ${completedTask.fileName} (代表 ${completedTask.metadata.originalFileCount} 个原始文件)`);
            } else {
              // 常规上传：触发单个文件回调
              group.callbacks.onFileUploaded(correspondingFile, completedTask);
              tusLogger.event(`单文件上传成功回调已触发: ${correspondingFile.name}`);
            }
          } catch (error) {
            tusLogger.error(`执行单文件上传成功回调失败: ${error}`);
          }
        }

        // 检查是否所有文件都已完成
        if (group.completedCount + group.errorCount >= group.taskIds.length) {
          // 获取所有完成的任务
          const completedTasks = group.taskIds.map((id) => tasks.value.get(id)).filter((task) => task && task.status === "completed") as UploadTask[];

          const failedTasks = group.taskIds.map((id) => tasks.value.get(id)).filter((task) => task && task.status === "error") as UploadTask[];

          if (completedTasks.length === group.taskIds.length) {
            // 所有文件都成功上传
            tusLogger.event(`上传组 ${groupId} 全部完成: ${completedTasks.length} 个文件`);
            if (group.callbacks?.onAllFilesUploaded) {
              try {
                // 检查是否为智能打包上传
                const isSmartPacked = completedTasks.some((task) => task.metadata?.uploadType === "smart-packed-archive");
                if (isSmartPacked) {
                  // 智能打包上传：传递压缩文件列表
                  const archiveFiles = completedTasks.map((task) => new File([], task.fileName, { type: "application/x-7z-compressed" }));
                  group.callbacks.onAllFilesUploaded(archiveFiles, completedTasks);
                  tusLogger.event(`智能打包全部文件上传成功回调已触发: ${archiveFiles.length} 个压缩文件`);
                } else {
                  // 常规上传：传递原始文件列表
                  group.callbacks.onAllFilesUploaded(group.files, completedTasks);
                  tusLogger.event(`全部文件上传成功回调已触发: ${group.files.length} 个文件`);
                }
              } catch (error) {
                tusLogger.error(`执行全部文件上传成功回调失败: ${error}`);
              }
            }
          } else if (failedTasks.length > 0) {
            // 有文件上传失败
            tusLogger.event(`上传组 ${groupId} 部分失败: 成功 ${completedTasks.length}, 失败 ${failedTasks.length}`);
            if (group.callbacks?.onUploadError) {
              try {
                const errorMessage = `${failedTasks.length} 个文件上传失败`;
                group.callbacks.onUploadError(errorMessage, failedTasks);
                tusLogger.event(`上传错误回调已触发: ${failedTasks.length} 个文件失败`);
              } catch (error) {
                tusLogger.error(`执行上传错误回调失败: ${error}`);
              }
            }
          }

          // 清理已完成的上传组
          uploadGroups.value.delete(groupId);
          tusLogger.event(`清理上传组: ${groupId}`);
        }

        break;
      }
    }
  };

  /**
   * 处理任务错误并更新上传组状态
   */
  const updateUploadGroupOnError = (taskId: string) => {
    for (const [groupId, group] of uploadGroups.value.entries()) {
      if (group.taskIds.includes(taskId)) {
        group.errorCount++;
        tusLogger.event(`上传组 ${groupId} 错误计数: ${group.errorCount}`);
        break;
      }
    }
  };

  /**
   * 智能文件上传：自动处理所有情况
   */
  const uploadFiles = async (files: File[], metadata?: Record<string, string>, callbacks?: UploadCallbacks): Promise<void> => {
    if (!isElectron.value) {
      throw new Error("只能在 Electron 环境中使用");
    }

    if (files.length === 0) {
      return;
    }

    try {
      // 创建上传组以跟踪此次上传
      const uploadGroupId = generateUniqueId("upload-group");

      // 分析文件组成
      const analysis = analyzeFileGroups(files);

      tusLogger.analysis(`📊 上传分析: ${files.length} 个文件`);
      tusLogger.analysis(`📁 文件夹数: ${analysis.folderGroups.length}`);
      tusLogger.analysis(`📄 单独文件数: ${analysis.singleFiles.length}`);
      tusLogger.analysis(`🔀 混合上传: ${analysis.hasMixedUpload ? "是" : "否"}`);
      tusLogger.analysis(`📦 将创建 ${analysis.totalGroups} 个独立上传任务`);

      // 创建上传组
      const uploadGroup: UploadGroup = {
        id: uploadGroupId,
        files: [...files],
        taskIds: [],
        taskFileMap: new Map(),
        callbacks,
        completedCount: 0,
        errorCount: 0,
        startTime: new Date(),
      };

      // 增强 metadata，添加上传组ID
      const enhancedMetadata = {
        ...metadata,
        uploadGroupId,
      };

      // 智能打包检测
      const smartPackingResult = await trySmartPacking(files, enhancedMetadata, uploadGroupId, callbacks, analysis);
      if (smartPackingResult.handled) {
        return;
      }

      // 处理混合上传或复杂场景
      if (analysis.hasMixedUpload || analysis.folderGroups.length > 1) {
        tusLogger.analysis(`🔀 检测到混合上传，为每个单独文件和文件夹创建独立任务`);
        await uploadMixedContent(analysis, enhancedMetadata, uploadGroup);
      } else if (analysis.folderGroups.length === 1) {
        // 单一文件夹上传
        tusLogger.analysis(`🗂️ 单一文件夹上传，保持目录结构`);
        await uploadAsBatch(files, enhancedMetadata, uploadGroup);
      } else if (analysis.singleFiles.length > 10) {
        // 大量单独文件
        tusLogger.analysis(`📚 大量单独文件 (${analysis.singleFiles.length} > 10)，使用批量上传便于管理`);
        await uploadAsBatch(files, enhancedMetadata, uploadGroup);
      } else {
        // 少量单独文件
        tusLogger.analysis(`📄 使用单独上传模式 (${analysis.singleFiles.length} 个文件)`);
        await uploadIndividually(files, enhancedMetadata, uploadGroup);
      }

      // 保存上传组
      uploadGroups.value.set(uploadGroupId, uploadGroup);
      tusLogger.event(`创建上传组: ${uploadGroupId}, 包含 ${files.length} 个文件`);
    } catch (error) {
      tusLogger.error("智能上传失败:", error);
      toast.error(`上传失败: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  };

  /**
   * 处理混合内容上传
   */
  const uploadMixedContent = async (analysis: ReturnType<typeof analyzeFileGroups>, metadata?: Record<string, string>, uploadGroup?: UploadGroup) => {
    const uploadTasks: Promise<void>[] = [];

    // 为每个单独文件创建独立任务
    for (const singleFile of analysis.singleFiles) {
      tusLogger.analysis(`📄 创建单文件任务: ${singleFile.name}`);
      uploadTasks.push(
        uploadIndividually(
          [singleFile],
          {
            ...metadata,
            uploadGroup: `single-file-${singleFile.name}`,
            isPartOfMixed: "true",
          },
          uploadGroup
        )
      );
    }

    // 为每个文件夹创建独立的批量任务
    for (const folder of analysis.folderGroups) {
      tusLogger.analysis(`📁 创建文件夹任务: ${folder.name} (${folder.files.length} 个文件)`);
      uploadTasks.push(
        uploadAsBatch(
          folder.files,
          {
            ...metadata,
            uploadGroup: `folder-${folder.name}`,
            isPartOfMixed: "true",
          },
          uploadGroup
        )
      );
    }

    // 并行执行所有上传任务
    try {
      await Promise.all(uploadTasks);
      toast.success(`混合上传已开始: ${analysis.singleFiles.length} 个单独文件 + ${analysis.folderGroups.length} 个文件夹`);
    } catch (error) {
      tusLogger.error("混合上传失败:", error);
      throw error;
    }
  };

  /**
   * 获取文件显示名称 - 简化版本，主要依赖Electron端处理
   */
  const getFileDisplayName = (file: File): string => {
    const relativePath = (file as any).webkitRelativePath as string;

    // 如果有相对路径，直接使用（Electron端已经处理过格式化）
    if (relativePath) {
      return relativePath;
    }

    // 否则使用文件名
    return file.name;
  };

  /**
   * 智能创建上传任务：自动选择最佳方式
   */
  const createUploadFromFile = async (
    file: File,
    metadata?: Record<string, string>,
    options?: {
      isSubTask?: boolean;
      batchId?: string;
    }
  ): Promise<string> => {
    if (!isElectron.value) {
      throw new Error("只能在 Electron 环境中使用");
    }

    try {
      const api = getElectronAPI();
      const filePath = (file as any).path as string;

      if (filePath) {
        // 优先使用文件路径，性能最佳
        tusLogger.task(`使用文件路径创建上传任务: ${file.name}`);

        const relativePath = (file as any).webkitRelativePath as string;

        // 使用简化的显示名称获取（主要依赖Electron端处理）
        let fileName = getFileDisplayName(file);

        if (relativePath) {
          tusLogger.task(`保持文件结构: ${fileName}`);
        }

        const enhancedMetadata = {
          ...metadata,
          originalName: fileName, // 使用格式化后的显示名称
          relativePath: relativePath || "",
          preserveStructure: relativePath ? "true" : "false",
          fileType: file.type,
          fileSize: file.size.toString(),
          uploadMethod: "file-path",
          // 添加子任务标识到 metadata
          isSubTask: options?.isSubTask ? "true" : "false",
          batchId: options?.batchId || "",
        };

        debug.upload.start(fileName, file.size);

        const response: ApiResponse = await api.tus.createUpload(filePath, enhancedMetadata);

        if (response.success && response.taskId) {
          return response.taskId;
        } else {
          throw new Error(response.error || "创建上传任务失败");
        }
      } else {
        // 备用方案：使用文件内容上传
        tusLogger.task(`使用文件内容创建上传任务: ${file.name}`);

        const relativePath = (file as any).webkitRelativePath as string;

        // 使用简化的显示名称获取（主要依赖Electron端处理）
        let fileName = getFileDisplayName(file);

        if (relativePath) {
          tusLogger.task(`保持文件结构: ${fileName}`);
        }

        const enhancedMetadata = {
          ...metadata,
          originalName: fileName, // 使用格式化后的显示名称
          relativePath: relativePath || "",
          preserveStructure: relativePath ? "true" : "false",
          fileType: file.type,
          fileSize: file.size.toString(),
          uploadMethod: "browser-file",
          // 添加子任务标识到 metadata
          isSubTask: options?.isSubTask ? "true" : "false",
          batchId: options?.batchId || "",
        };

        debug.upload.start(fileName, file.size);

        const response: ApiResponse = await api.tus.createUploadFromFile({
          name: fileName,
          content: await file.arrayBuffer(),
          metadata: enhancedMetadata,
        });

        if (response.success && response.taskId) {
          return response.taskId;
        } else {
          throw new Error(response.error || "创建上传任务失败");
        }
      }
    } catch (error) {
      tusLogger.error("从 File 对象创建上传任务失败:", error);
      throw error;
    }
  };

  /**
   * 批量上传
   */
  const uploadAsBatch = async (files: File[], metadata?: Record<string, string>, uploadGroup?: UploadGroup) => {
    // 检查是否有文件夹结构
    const hasRelativePaths = files.some((file) => (file as any).webkitRelativePath);

    let batchName: string;
    let folderPath: string | undefined;

    if (hasRelativePaths) {
      // 有文件夹结构，提取根文件夹名称
      const firstFileWithPath = files.find((file) => (file as any).webkitRelativePath);
      if (firstFileWithPath) {
        const relativePath = (firstFileWithPath as any).webkitRelativePath;
        const pathParts = relativePath.split("/");
        const rootFolderName = pathParts[0];

        batchName = `${rootFolderName}`;
        folderPath = rootFolderName;

        tusLogger.batch(`检测到文件夹结构上传: ${rootFolderName}, 共 ${files.length} 个文件`);
      } else {
        batchName = `批量上传-${new Date().toLocaleTimeString()}`;
      }
    } else {
      batchName = `批量上传-${new Date().toLocaleTimeString()}`;
    }

    try {
      const batchId = await createBatchUpload(files, batchName, folderPath, metadata);

      // 获取批量任务的子任务ID并添加到上传组
      if (uploadGroup) {
        const batch = batchTasks.value.get(batchId);
        if (batch && batch.subTasks) {
          uploadGroup.taskIds.push(...batch.subTasks);

          // 建立批量任务中每个子任务到文件的映射关系
          batch.subTasks.forEach((subTaskId, index) => {
            if (index < files.length) {
              uploadGroup.taskFileMap.set(subTaskId, files[index]);
            }
          });
        }
      }

      const success = await startBatchUpload(batchId);

      if (success) {
        if (hasRelativePaths) {
          toast.success(`文件夹上传已开始: ${files.length} 个文件`);
        } else {
          toast.success(`批量上传已开始: ${files.length} 个文件`);
        }
      } else {
        throw new Error("批量上传启动失败");
      }
    } catch (error) {
      tusLogger.error("批量上传失败:", error);
      throw error;
    }
  };

  /**
   * 单独上传
   */
  const uploadIndividually = async (files: File[], metadata?: Record<string, string>, uploadGroup?: UploadGroup) => {
    const taskIds: string[] = [];
    const failedFiles: string[] = [];

    // 创建所有任务
    for (const file of files) {
      try {
        const taskId = await createUploadFromFile(file, metadata);
        taskIds.push(taskId);

        // 建立任务ID到文件的映射关系
        if (uploadGroup) {
          uploadGroup.taskFileMap.set(taskId, file);
        }
      } catch (error) {
        tusLogger.error(`创建任务失败: ${file.name}`, error);
        failedFiles.push(file.name);
      }
    }

    // 将任务ID添加到上传组
    if (uploadGroup) {
      uploadGroup.taskIds.push(...taskIds);
    }

    // 启动所有任务
    for (const taskId of taskIds) {
      try {
        await startUpload(taskId);
      } catch (error) {
        tusLogger.error("启动上传失败:", error);
      }
    }

    if (failedFiles.length === 0) {
      toast.success(`已添加 ${taskIds.length} 个文件到上传队列`);
    } else {
      toast.warning(`${failedFiles.length} 个文件添加失败`);
    }
  };

  /**
   * 通过文件选择对话框上传
   */
  const uploadFromDialog = async (): Promise<void> => {
    if (!isElectron.value) {
      throw new Error("只能在 Electron 环境中使用");
    }

    try {
      const api = getElectronAPI();
      const response: ApiResponse = await api.tus.createUploadFromDialog();

      if (response.success && response.data?.taskIds) {
        const taskIds = response.data.taskIds;

        // 启动所有任务
        for (const taskId of taskIds) {
          try {
            await startUpload(taskId);
          } catch (error) {
            tusLogger.error("启动上传失败:", error);
          }
        }

        toast.success(`已添加 ${taskIds.length} 个文件到上传队列`);
      } else if (response.error === "用户取消") {
        // 用户取消选择，静默处理
        return;
      } else {
        throw new Error(response.error || "选择文件失败");
      }
    } catch (error) {
      tusLogger.error("文件选择上传失败:", error);
      toast.error(`文件选择失败: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  };

  /**
   * 开始上传任务
   */
  const startUpload = async (taskId: string): Promise<boolean> => {
    if (!isElectron.value) return false;

    try {
      const response: ApiResponse = await getElectronAPI().tus.startUpload(taskId);
      return response.success;
    } catch (error) {
      tusLogger.error("开始上传失败:", error);
      return false;
    }
  };

  /**
   * 暂停上传任务
   */
  const pauseUpload = async (taskId: string): Promise<boolean> => {
    if (!isElectron.value) return false;

    try {
      const response: ApiResponse = await getElectronAPI().tus.pauseUpload(taskId);
      return response.success;
    } catch (error) {
      tusLogger.error("暂停上传失败:", error);
      return false;
    }
  };

  /**
   * 恢复上传任务
   */
  const resumeUpload = async (taskId: string): Promise<boolean> => {
    if (!isElectron.value) return false;

    try {
      const response: ApiResponse = await getElectronAPI().tus.resumeUpload(taskId);
      return response.success;
    } catch (error) {
      tusLogger.error("恢复上传失败:", error);
      return false;
    }
  };

  /**
   * 取消上传任务
   */
  const cancelUpload = async (taskId: string): Promise<boolean> => {
    if (!isElectron.value) return false;

    try {
      const response: ApiResponse = await getElectronAPI().tus.cancelUpload(taskId);
      return response.success;
    } catch (error) {
      tusLogger.error("取消上传失败:", error);
      return false;
    }
  };

  /**
   * 重试上传任务
   */
  const retryUpload = async (taskId: string): Promise<boolean> => {
    if (!isElectron.value) return false;

    try {
      const response: ApiResponse = await getElectronAPI().tus.retryUpload(taskId);
      return response.success;
    } catch (error) {
      tusLogger.error("重试上传失败:", error);
      return false;
    }
  };

  /**
   * 删除任务
   */
  const deleteTask = async (taskId: string): Promise<boolean> => {
    if (!isElectron.value) return false;

    try {
      const response: ApiResponse = await getElectronAPI().tus.deleteTask(taskId);
      if (response.success) {
        tasks.value.delete(taskId);
      }
      return response.success;
    } catch (error) {
      tusLogger.error("删除任务失败:", error);
      return false;
    }
  };

  /**
   * 批量操作
   */
  const startAllPausedTasks = async () => {
    const promises = pausedTasks.value.map((task) => startUpload(task.id));
    await Promise.all(promises);
  };

  const pauseAllUploadingTasks = async () => {
    const promises = uploadingTasks.value.map((task) => pauseUpload(task.id));
    await Promise.all(promises);
  };

  const retryAllErrorTasks = async () => {
    const promises = errorTasks.value.map((task) => retryUpload(task.id));
    await Promise.all(promises);
  };

  /**
   * 更新配置
   */
  const updateConfig = async (config: Partial<TusUploadConfig>): Promise<boolean> => {
    if (!isElectron.value) return false;

    try {
      const response: ApiResponse = await getElectronAPI().tus.updateConfig(config);
      return response.success;
    } catch (error) {
      tusLogger.error("更新配置失败:", error);
      return false;
    }
  };

  /**
   * 清理已完成的任务
   */
  const clearCompletedTasks = async (): Promise<boolean> => {
    if (!isElectron.value) return false;

    try {
      const response: ApiResponse = await getElectronAPI().tus.clearCompletedTasks();
      if (response.success) {
        completedTasks.value.forEach((task) => {
          tasks.value.delete(task.id);
        });
      }
      return response.success;
    } catch (error) {
      tusLogger.error("清理已完成任务失败:", error);
      return false;
    }
  };

  /**
   * 清空所有任务
   */
  const clearAllTasks = async (): Promise<boolean> => {
    if (!isElectron.value) return false;

    try {
      const response: ApiResponse = await getElectronAPI().tus.clearAllTasks();
      if (response.success) {
        // 清空前端的所有任务
        tasks.value.clear();
        batchTasks.value.clear();
        tusLogger.task("已清空所有上传任务");
      }
      return response.success;
    } catch (error) {
      tusLogger.error("清空所有任务失败:", error);
      return false;
    }
  };

  /**
   * 获取任务的上传URL
   */
  /**
   * 确保任务完整性：获取并同步 uploadUrl
   */
  const ensureTaskCompleteness = async (taskId: string): Promise<UploadTask | undefined> => {
    const task = tasks.value.get(taskId);
    if (!task) return undefined;

    // 如果任务已经有 uploadUrl，直接返回
    if (task.uploadUrl) {
      return task;
    }

    // 尝试从后端获取完整的任务信息
    try {
      const api = getElectronAPI();
      const response: ApiResponse = await api.tus.getTask(taskId);

      if (response.success && response.task) {
        const completeTask = response.task;
        // 更新本地任务缓存
        tasks.value.set(taskId, completeTask);
        tusLogger.task(`同步完整任务信息: ${completeTask.fileName}, uploadUrl: ${completeTask.uploadUrl}`);
        return completeTask;
      }
    } catch (error) {
      tusLogger.warn(`获取完整任务信息失败: ${taskId}`, error);
    }

    return task;
  };

  const getTaskUploadUrl = async (taskId: string): Promise<string | undefined> => {
    if (!isElectron.value) {
      throw new Error("只能在 Electron 环境中使用");
    }

    try {
      // 首先尝试从本地任务缓存获取
      const localTask = tasks.value.get(taskId);
      if (localTask && localTask.uploadUrl) {
        return localTask.uploadUrl;
      }

      // 如果本地没有，从后端获取
      const api = getElectronAPI();
      const response: ApiResponse = await api.tus.getTaskUploadUrl(taskId);

      if (response.success && response.data) {
        // 同时更新本地任务缓存中的 uploadUrl
        if (localTask) {
          const updatedTask = { ...localTask, uploadUrl: response.data.uploadUrl };
          tasks.value.set(taskId, updatedTask);
        }
        return response.data.uploadUrl;
      } else {
        throw new Error(response.error || "获取上传URL失败");
      }
    } catch (error) {
      tusLogger.error("获取上传URL失败:", error);
      return undefined;
    }
  };

  /**
   * 刷新任务列表
   */
  const refreshTasks = async () => {
    if (!isElectron.value) return;

    try {
      const response: ApiResponse = await getElectronAPI().tus.getAllTasks();
      if (response.success && response.tasks) {
        tusLogger.task(`获取到 ${response.tasks.length} 个TUS任务`);

        tasks.value.clear();
        response.tasks.forEach((task) => {
          tasks.value.set(task.id, task);
        });

        if (import.meta.env.DEV) {
          response.tasks.forEach((task) => {
            tusLogger.task(`任务: ${task.fileName} - ${task.status} (${task.progress}%)`);
          });
        }

        tusLogger.task(`已同步 ${response.tasks.length} 个任务到前端`);
      } else {
        tusLogger.warn("获取TUS任务失败", response);
      }
    } catch (error) {
      tusLogger.error("同步TUS任务时发生错误", error);
    }
  };

  /**
   * 工具函数
   */
  const formatUploadSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + "/s";
  };

  const formatRemainingTime = (seconds: number): string => {
    if (seconds === Infinity || isNaN(seconds)) return "计算中...";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
    }
  };

  const setupEventListeners = () => {
    if (!isElectron.value) {
      tusLogger.warn("TUS上传功能仅在Electron环境中可用");
      return;
    }

    if (isListenersSetup.value) {
      tusLogger.event("TUS事件监听器已设置，跳过重复设置");
      return;
    }

    try {
      const api = getElectronAPI();

      if (!api || !api.tus) {
        throw new Error("Electron TUS API 不可用");
      }

      api.tus.onUploadTaskCreated(handleTaskCreated);
      api.tus.onUploadTaskProgress(handleTaskProgress);
      api.tus.onUploadTaskStatusChanged(handleTaskStatusChanged);
      api.tus.onUploadTaskCompleted(handleTaskCompleted);
      api.tus.onUploadTaskError(handleTaskError);

      isListenersSetup.value = true;
      tusLogger.event("TUS事件监听器设置完成");
    } catch (error) {
      tusLogger.error("设置 TUS 事件监听器失败:", error);
      throw error;
    }
  };

  const cleanupEventListeners = () => {
    if (!isElectron.value) return;

    if (!isListenersSetup.value) {
      tusLogger.event("TUS事件监听器未设置，跳过清理");
      return;
    }

    try {
      const api = getElectronAPI();

      if (!api || !api.tus) {
        tusLogger.warn("Electron TUS API 不可用，跳过事件清理");
        isListenersSetup.value = false;
        return;
      }

      api.tus.removeAllListeners("upload-task-created");
      api.tus.removeAllListeners("upload-task-progress");
      api.tus.removeAllListeners("upload-task-status-changed");
      api.tus.removeAllListeners("upload-task-completed");
      api.tus.removeAllListeners("upload-task-error");

      isListenersSetup.value = false;
      tusLogger.event("TUS事件监听器清理完成");
    } catch (error) {
      tusLogger.error("清理 TUS 事件监听器失败:", error);
      isListenersSetup.value = false;
    }
  };

  /**
   * 创建批量上传任务
   */
  const createBatchUpload = async (files: File[], batchName: string, folderPath?: string, metadata?: Record<string, string>): Promise<string> => {
    if (!isElectron.value) {
      throw new Error("只能在 Electron 环境中使用");
    }

    try {
      const batchId = generateUniqueId("batch");
      debug.upload.batch(batchName, files.length);
      tusLogger.batch(`创建批量上传任务: ${batchName} (${files.length}个文件), 文件夹路径: ${folderPath || "无"}`);

      const totalSize = files.reduce((sum, file) => sum + file.size, 0);

      const batchTask: BatchUploadTask = {
        id: batchId,
        type: "batch",
        batchName,
        folderPath: folderPath || "",
        totalFiles: files.length,
        completedFiles: 0,
        failedFiles: 0,
        pausedFiles: 0,
        totalSize,
        uploadedSize: 0,
        progress: 0,
        status: "pending",
        startTime: new Date(),
        subTasks: [],
        expanded: false,
        metadata,
        avgUploadSpeed: 0,
        estimatedRemainingTime: 0,
      };

      batchTasks.value.set(batchId, batchTask);

      const createdTaskIds: string[] = [];
      for (const file of files) {
        try {
          // 直接在创建任务时传递子任务标识
          const taskId = await createUploadFromFile(file, metadata, {
            isSubTask: true,
            batchId: batchId,
          });
          createdTaskIds.push(taskId);
          tusLogger.batch(`子任务创建成功: ${file.name} -> batchId: ${batchId}`);
        } catch (error) {
          tusLogger.error(`创建子任务失败: ${file.name}`, error);
        }
      }

      const updatedBatchTask = {
        ...batchTask,
        totalFiles: createdTaskIds.length,
        subTasks: createdTaskIds,
      };
      batchTasks.value.set(batchId, updatedBatchTask);

      updateBatchTaskProgress(batchId);

      tusLogger.batch(`✅ 批量任务创建完成: ${batchName}, 成功创建 ${createdTaskIds.length} 个子任务`);
      debug.upload.batch(`批量任务创建完成: ${batchName}`, createdTaskIds.length);
      return batchId;
    } catch (error) {
      tusLogger.error("创建批量上传任务失败:", error);
      throw error;
    }
  };

  /**
   * 更新批量任务进度
   */
  const updateBatchTaskProgress = (batchId: string) => {
    const batch = batchTasks.value.get(batchId);
    if (!batch) return;

    const subTasks = batch.subTasks.map((id) => tasks.value.get(id)).filter((task) => task !== undefined) as UploadTask[];

    if (subTasks.length === 0) return;

    if (batch.totalFiles !== subTasks.length) {
      tusLogger.warn(`⚠️ 批量任务 ${batch.batchName} 的 totalFiles (${batch.totalFiles}) 与实际子任务数 (${subTasks.length}) 不匹配，正在修正...`);
      tusLogger.batch(`批量任务 ${batch.batchName} 文件数不匹配，正在修正: 期望=${batch.totalFiles}, 实际=${subTasks.length}`);
    }

    let needsProgressFix = false;
    subTasks.forEach((task) => {
      if (task.status === "completed" && task.progress !== 100) {
        tusLogger.warn(`⚠️ 发现已完成任务进度不是100%: ${task.fileName} (${task.progress}%)，正在修正...`);
        const updatedTask = { ...task, progress: 100 };
        tasks.value.set(task.id, updatedTask);
        needsProgressFix = true;
      }
    });

    const correctedSubTasks = needsProgressFix ? (batch.subTasks.map((id) => tasks.value.get(id)).filter((task) => task !== undefined) as UploadTask[]) : subTasks;

    const completedFiles = correctedSubTasks.filter((task) => task.status === "completed").length;
    const failedFiles = correctedSubTasks.filter((task) => task.status === "error").length;
    const pausedFiles = correctedSubTasks.filter((task) => task.status === "paused").length;
    const uploadingFiles = correctedSubTasks.filter((task) => task.status === "uploading").length;

    const totalProgress = correctedSubTasks.reduce((sum, task) => sum + task.progress, 0) / correctedSubTasks.length;
    const uploadedSize = correctedSubTasks.reduce((sum, task) => sum + task.bytesUploaded, 0);

    const avgUploadSpeed = correctedSubTasks.filter((task) => task.status === "uploading").reduce((sum, task) => sum + task.uploadSpeed, 0) / Math.max(uploadingFiles, 1);

    const remainingSize = batch.totalSize - uploadedSize;
    const estimatedRemainingTime = avgUploadSpeed > 0 ? remainingSize / avgUploadSpeed : 0;

    const actualTotalFiles = correctedSubTasks.length;

    let batchStatus: UploadStatus = "pending";
    if (completedFiles === actualTotalFiles) {
      batchStatus = "completed";
      tusLogger.batch(`✅ 批量任务完成: ${batch.batchName} (${completedFiles}/${actualTotalFiles})`);
      debug.upload.complete(`批量任务: ${batch.batchName} (${completedFiles}/${actualTotalFiles})`);
    } else if (failedFiles > 0 && completedFiles + failedFiles === actualTotalFiles) {
      batchStatus = "error";
      tusLogger.batch(`❌ 批量任务失败: ${batch.batchName} (完成=${completedFiles}, 失败=${failedFiles}, 总计=${actualTotalFiles})`);
      debug.upload.error(`批量任务: ${batch.batchName}`, `失败: ${failedFiles}个, 完成: ${completedFiles}个, 总计: ${actualTotalFiles}个`);
    } else if (uploadingFiles > 0) {
      batchStatus = "uploading";
    } else if (pausedFiles > 0) {
      batchStatus = "paused";
    }

    const updatedBatch: BatchUploadTask = {
      ...batch,
      totalFiles: actualTotalFiles,
      completedFiles,
      failedFiles,
      pausedFiles,
      progress: Math.round(totalProgress),
      uploadedSize,
      status: batchStatus,
      avgUploadSpeed,
      estimatedRemainingTime,
      endTime: batchStatus === "completed" ? new Date() : batch.endTime,
    };

    batchTasks.value.set(batchId, updatedBatch);
  };

  /**
   * 切换批量任务展开状态
   */
  const toggleBatchExpanded = (batchId: string) => {
    const batch = batchTasks.value.get(batchId);
    if (batch) {
      const updatedBatch = { ...batch, expanded: !batch.expanded };
      batchTasks.value.set(batchId, updatedBatch);
    }
  };

  /**
   * 获取批量任务的子任务
   */
  const getBatchSubTasks = (batchId: string): UploadTask[] => {
    const batch = batchTasks.value.get(batchId);
    if (!batch) return [];

    return batch.subTasks.map((id) => tasks.value.get(id)).filter((task) => task !== undefined) as UploadTask[];
  };

  /**
   * 批量任务操作
   */
  const startBatchUpload = async (batchId: string): Promise<boolean> => {
    const batch = batchTasks.value.get(batchId);
    if (!batch) return false;

    const results = await Promise.all(batch.subTasks.map((taskId) => startUpload(taskId)));
    return results.every((result: boolean) => result === true);
  };

  const pauseBatchUpload = async (batchId: string): Promise<boolean> => {
    const batch = batchTasks.value.get(batchId);
    if (!batch) return false;

    const results = await Promise.all(batch.subTasks.map((taskId) => pauseUpload(taskId)));
    return results.every((result: boolean) => result === true);
  };

  const resumeBatchUpload = async (batchId: string): Promise<boolean> => {
    const batch = batchTasks.value.get(batchId);
    if (!batch) return false;

    const pausedSubTasks = batch.subTasks.filter((taskId) => {
      const task = tasks.value.get(taskId);
      return task?.status === "paused";
    });

    if (pausedSubTasks.length === 0) return true;

    const results = await Promise.all(pausedSubTasks.map((taskId) => resumeUpload(taskId)));
    return results.every((result: boolean) => result === true);
  };

  const retryBatchUpload = async (batchId: string): Promise<boolean> => {
    const batch = batchTasks.value.get(batchId);
    if (!batch) return false;

    const failedSubTasks = batch.subTasks.filter((taskId) => {
      const task = tasks.value.get(taskId);
      return task?.status === "error";
    });

    if (failedSubTasks.length === 0) return true;

    const results = await Promise.all(failedSubTasks.map((taskId) => retryUpload(taskId)));
    return results.every((result: boolean) => result === true);
  };

  const deleteBatchTask = async (batchId: string): Promise<boolean> => {
    const batch = batchTasks.value.get(batchId);
    if (!batch) return false;

    const results = await Promise.all(batch.subTasks.map((taskId) => deleteTask(taskId)));
    batchTasks.value.delete(batchId);

    return results.every((result: boolean) => result === true);
  };

  // 组件挂载和卸载
  onMounted(async () => {
    try {
      setupEventListeners();
      await refreshTasks();
    } catch (error) {
      tusLogger.error("TUS 模块初始化失败:", error);
    }
  });

  onUnmounted(() => {
    try {
      cleanupEventListeners();
    } catch (error) {
      tusLogger.error("TUS 模块清理失败:", error);
    }
  });

  // 初始化事件监听器（单例模式下，不依赖组件生命周期）
  if (isElectron.value) {
    setupEventListeners();
    refreshTasks().catch((error) => {
      tusLogger.error("TUS 模块初始化失败:", error);
    });
  }

  return {
    // 状态
    isElectron,
    tasks: allTasks,
    batchTasks: allBatchTasks,
    displayTasks,
    standaloneTasks,
    activeTasks,
    uploadingTasks,
    pausedTasks,
    completedTasks,
    errorTasks,
    activeBatchTasks,
    uploadingBatchTasks,
    totalProgress,

    // 主要方法（简化的API）
    uploadFiles,
    uploadFromDialog,

    // 任务操作
    startUpload,
    pauseUpload,
    resumeUpload,
    cancelUpload,
    retryUpload,
    deleteTask,

    // 批量操作
    startAllPausedTasks,
    pauseAllUploadingTasks,
    retryAllErrorTasks,
    clearCompletedTasks,
    clearAllTasks,

    // URL获取
    getTaskUploadUrl,

    // 配置
    updateConfig,

    // 工具函数
    formatFileSize,
    formatUploadSpeed,
    formatRemainingTime,

    // 其他
    refreshTasks,

    // 清理函数（供外部手动调用）
    cleanup: cleanupEventListeners,

    // 批量任务管理函数
    createBatchUpload,
    updateBatchTaskProgress,
    toggleBatchExpanded,
    getBatchSubTasks,
    startBatchUpload,
    pauseBatchUpload,
    resumeBatchUpload,
    retryBatchUpload,
    deleteBatchTask,

    // 日志管理函数
    ...useTusLogger(),
  };
}

export function useTusUpload() {
  if (!tusUploadInstance) {
    tusUploadInstance = createTusUploadInstance();
  }
  return tusUploadInstance;
}
